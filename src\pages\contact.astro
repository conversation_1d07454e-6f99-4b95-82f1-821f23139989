---
import Layout from '../layouts/Layout.astro';
import { getEntry } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { contact } = homepageContent.data;
---

<Layout
  title="Contact | Nob Hokleng | Software Developer & System Architect"
  description="Get in touch with <PERSON><PERSON> for software development projects, consulting, or collaboration opportunities."
  keywords="contact, hire developer, software development services, consulting, collaboration, freelance developer"
>
  <section class="contact pt-32 pb-24 bg-white">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 class="text-4xl font-bold text-center mb-12 font-heading relative">
        Contact
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-15 items-start">
        <div class="contact-info bg-light p-10 rounded-2xl shadow-lg">
          <p class="mb-8 text-lg leading-relaxed text-text">
            {contact.introText}
          </p>
          
          <div class="space-y-8">
            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">Connect With Me</h3>
              <div class="space-y-4">
                <a href={contact.social.linkedin} target="_blank" rel="noopener" class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group" aria-label="Connect with me on LinkedIn">
                  <i class="fab fa-linkedin text-lg group-hover:scale-110 transition-transform" aria-hidden="true"></i>
                  <div class="flex flex-col">
                    <span class="font-semibold">LinkedIn</span>
                    <span class="text-xs opacity-90">Professional network</span>
                  </div>
                </a>
                <a href={contact.social.github} target="_blank" rel="noopener" class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-gray-800 to-black text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group" aria-label="View my projects on GitHub">
                  <i class="fab fa-github text-lg group-hover:scale-110 transition-transform" aria-hidden="true"></i>
                  <div class="flex flex-col">
                    <span class="font-semibold">GitHub</span>
                    <span class="text-xs opacity-90">Code repositories</span>
                  </div>
                </a>
                <a href="mailto:<EMAIL>" class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group" aria-label="Send me an email">
                  <i class="fas fa-envelope text-lg group-hover:scale-110 transition-transform" aria-hidden="true"></i>
                  <div class="flex flex-col">
                    <span class="font-semibold">Email</span>
                    <span class="text-xs opacity-90"><EMAIL></span>
                  </div>
                </a>
              </div>
            </div>
            
            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">
                <i class="fas fa-clock mr-2 text-accent"></i>
                Response Time
              </h3>
              <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border-l-4 border-primary">
                <p class="text-text text-sm leading-relaxed mb-2">
                  <i class="fas fa-check-circle text-green-500 mr-2"></i>
                  {contact.responseTime}
                </p>
                <p class="text-gray-500 text-xs">
                  I prioritize responding to job opportunities and project inquiries promptly.
                </p>
              </div>
            </div>

            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">
                <i class="fas fa-map-marker-alt mr-2 text-accent"></i>
                Location & Availability
              </h3>
              <div class="space-y-3">
                <div class="flex items-center gap-3">
                  <i class="fas fa-globe text-primary"></i>
                  <span class="text-text text-sm">Remote work worldwide</span>
                </div>
                <div class="flex items-center gap-3">
                  <i class="fas fa-clock text-primary"></i>
                  <span class="text-text text-sm">GMT+7 timezone (flexible hours)</span>
                </div>
                <div class="flex items-center gap-3">
                  <i class="fas fa-briefcase text-primary"></i>
                  <span class="text-text text-sm">Available for new opportunities</span>
                </div>
              </div>
            </div>

            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">
                <i class="fas fa-handshake mr-2 text-accent"></i>
                Project Types
              </h3>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-center gap-2">
                  <i class="fas fa-check text-green-500 text-sm"></i>
                  <span class="text-sm text-text">Full-time positions</span>
                </div>
                <div class="flex items-center gap-2">
                  <i class="fas fa-check text-green-500 text-sm"></i>
                  <span class="text-sm text-text">Contract work</span>
                </div>
                <div class="flex items-center gap-2">
                  <i class="fas fa-check text-green-500 text-sm"></i>
                  <span class="text-sm text-text">Consulting projects</span>
                </div>
                <div class="flex items-center gap-2">
                  <i class="fas fa-check text-green-500 text-sm"></i>
                  <span class="text-sm text-text">Technical collaborations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="contact-form bg-white p-10 rounded-2xl shadow-lg">
          <h3 class="text-2xl font-bold text-secondary mb-5 font-heading">Get In Touch</h3>
          
          <!-- Success/Error Messages -->
          <div id="form-messages" class="mb-6 hidden">
            <div id="success-message" class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-4 hidden">
              <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>Thank you! Your message has been sent successfully. I'll get back to you soon.</span>
              </div>
            </div>
            <div id="error-message" class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-4 hidden">
              <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span id="error-text">Something went wrong. Please try again or contact me directly via LinkedIn.</span>
              </div>
            </div>
          </div>

          <form id="contact-form" class="space-y-6" novalidate>
            <div class="form-field">
              <label for="name" class="block font-semibold text-text text-sm mb-2">
                Name <span class="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                required 
                class="w-full p-3 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none transition-colors duration-300"
                placeholder="Your full name"
                aria-describedby="name-error"
              >
              <div id="name-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
            </div>

            <div class="form-field">
              <label for="email" class="block font-semibold text-text text-sm mb-2">
                Email <span class="text-red-500">*</span>
              </label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required 
                class="w-full p-3 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none transition-colors duration-300"
                placeholder="<EMAIL>"
                aria-describedby="email-error"
              >
              <div id="email-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
            </div>

            <div class="form-field">
              <label for="subject" class="block font-semibold text-text text-sm mb-2">
                Subject <span class="text-red-500">*</span>
              </label>
              <select 
                id="subject" 
                name="subject" 
                required 
                class="w-full p-3 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none transition-colors duration-300"
                aria-describedby="subject-error"
              >
                <option value="">Select a subject</option>
                <option value="job-opportunity">Job Opportunity</option>
                <option value="freelance-project">Freelance Project</option>
                <option value="collaboration">Collaboration</option>
                <option value="consulting">Consulting</option>
                <option value="other">Other</option>
              </select>
              <div id="subject-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
            </div>

            <div class="form-field">
              <label for="message" class="block font-semibold text-text text-sm mb-2">
                Message <span class="text-red-500">*</span>
              </label>
              <textarea 
                id="message" 
                name="message" 
                required 
                rows="5" 
                class="w-full p-3 border-2 border-gray-200 rounded-lg focus:border-primary focus:outline-none transition-colors duration-300 resize-vertical"
                placeholder="Tell me about your project or opportunity..."
                aria-describedby="message-error"
              ></textarea>
              <div id="message-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
              <div class="text-sm text-gray-500 mt-1">
                <span id="char-count">0</span>/1000 characters
              </div>
            </div>

            <!-- Honeypot field for spam protection -->
            <div class="hidden">
              <label for="website">Website (leave blank)</label>
              <input type="text" id="website" name="website" tabindex="-1" autocomplete="off">
            </div>

            <div class="form-field">
              <button 
                type="submit" 
                id="submit-btn"
                class="w-full px-7 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                aria-describedby="submit-status"
              >
                <span id="submit-text">Send Message</span>
                <i id="submit-spinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
              </button>
              <div id="submit-status" class="text-sm text-gray-500 mt-2 text-center" aria-live="polite"></div>
            </div>

            <div class="form-note bg-light p-4 rounded-lg text-sm text-gray-600">
              <i class="fas fa-info-circle mr-2"></i>
              Your information is secure and will only be used to respond to your inquiry. I typically respond within 24-48 hours.
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>
</Layout>

<script is:inline>
  // Form validation and submission handling
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitSpinner = document.getElementById('submit-spinner');
    const submitStatus = document.getElementById('submit-status');
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    
    // Character counter for message field
    if (messageTextarea && charCount) {
      messageTextarea.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        
        if (count > 1000) {
          charCount.style.color = '#ef4444';
          this.value = this.value.substring(0, 1000);
          charCount.textContent = '1000';
        } else if (count > 900) {
          charCount.style.color = '#f59e0b';
        } else {
          charCount.style.color = '#6b7280';
        }
      });
    }
    
    // Real-time validation
    const fields = ['name', 'email', 'subject', 'message'];
    fields.forEach(fieldName => {
      const field = document.getElementById(fieldName);
      if (field) {
        field.addEventListener('blur', () => validateField(fieldName));
        field.addEventListener('input', () => clearFieldError(fieldName));
      }
    });
    
    // Form submission
    if (form) {
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Validate all fields
        let isValid = true;
        fields.forEach(fieldName => {
          if (!validateField(fieldName)) {
            isValid = false;
          }
        });
        
        if (!isValid) {
          showMessage('Please correct the errors above.', 'error');
          return;
        }
        
        // Check honeypot
        const honeypot = document.getElementById('website');
        if (honeypot && honeypot.value.trim() !== '') {
          showMessage('Invalid submission detected.', 'error');
          return;
        }
        
        // Submit form
        await submitForm();
      });
    }
    
    function validateField(fieldName) {
      const field = document.getElementById(fieldName);
      const errorDiv = document.getElementById(fieldName + '-error');
      
      if (!field || !errorDiv) return true;
      
      let isValid = true;
      let errorMessage = '';
      
      const value = field.value.trim();
      
      switch (fieldName) {
        case 'name':
          if (!value) {
            errorMessage = 'Name is required.';
            isValid = false;
          } else if (value.length < 2) {
            errorMessage = 'Name must be at least 2 characters.';
            isValid = false;
          }
          break;
          
        case 'email':
          if (!value) {
            errorMessage = 'Email is required.';
            isValid = false;
          } else if (!isValidEmail(value)) {
            errorMessage = 'Please enter a valid email address.';
            isValid = false;
          }
          break;
          
        case 'subject':
          if (!value) {
            errorMessage = 'Please select a subject.';
            isValid = false;
          }
          break;
          
        case 'message':
          if (!value) {
            errorMessage = 'Message is required.';
            isValid = false;
          } else if (value.length < 10) {
            errorMessage = 'Message must be at least 10 characters.';
            isValid = false;
          } else if (value.length > 1000) {
            errorMessage = 'Message must not exceed 1000 characters.';
            isValid = false;
          }
          break;
      }
      
      if (isValid) {
        field.classList.remove('border-red-500');
        field.classList.add('border-green-500');
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
      } else {
        field.classList.remove('border-green-500');
        field.classList.add('border-red-500');
        errorDiv.textContent = errorMessage;
        errorDiv.classList.remove('hidden');
      }
      
      return isValid;
    }
    
    function clearFieldError(fieldName) {
      const field = document.getElementById(fieldName);
      const errorDiv = document.getElementById(fieldName + '-error');
      
      if (field && errorDiv) {
        field.classList.remove('border-red-500', 'border-green-500');
        errorDiv.classList.add('hidden');
      }
    }
    
    function isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }
    
    async function submitForm() {
      // Show loading state
      setSubmitLoading(true);
      
      try {
        const formData = new FormData(form);
        
        // Use Formspree for form handling (free tier)
        const response = await fetch('https://formspree.io/f/xkgnklvb', {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json'
          }
        });
        
        if (response.ok) {
          showMessage('Thank you! Your message has been sent successfully. I\'ll get back to you soon.', 'success');
          form.reset();
          clearAllFieldStyles();
          if (charCount) charCount.textContent = '0';
        } else {
          const data = await response.json();
          if (data.errors) {
            showMessage('Please check your form and try again.', 'error');
          } else {
            showMessage('Something went wrong. Please try again or contact me directly via LinkedIn.', 'error');
          }
        }
      } catch (error) {
        console.error('Form submission error:', error);
        showMessage('Network error. Please check your connection and try again.', 'error');
      } finally {
        setSubmitLoading(false);
      }
    }
    
    function setSubmitLoading(loading) {
      if (loading) {
        submitBtn.disabled = true;
        submitText.textContent = 'Sending...';
        submitSpinner.classList.remove('hidden');
        submitStatus.textContent = 'Please wait while we send your message...';
      } else {
        submitBtn.disabled = false;
        submitText.textContent = 'Send Message';
        submitSpinner.classList.add('hidden');
        submitStatus.textContent = '';
      }
    }
    
    function showMessage(message, type) {
      const messagesDiv = document.getElementById('form-messages');
      const successDiv = document.getElementById('success-message');
      const errorDiv = document.getElementById('error-message');
      const errorText = document.getElementById('error-text');
      
      if (!messagesDiv || !successDiv || !errorDiv || !errorText) return;
      
      // Hide all messages first
      successDiv.classList.add('hidden');
      errorDiv.classList.add('hidden');
      
      // Show appropriate message
      if (type === 'success') {
        successDiv.classList.remove('hidden');
      } else {
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
      }
      
      messagesDiv.classList.remove('hidden');
      
      // Scroll to message
      messagesDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // Auto-hide success messages after 10 seconds
      if (type === 'success') {
        setTimeout(() => {
          successDiv.classList.add('hidden');
          messagesDiv.classList.add('hidden');
        }, 10000);
      }
    }
    
    function clearAllFieldStyles() {
      fields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
          field.classList.remove('border-red-500', 'border-green-500');
        }
      });
    }
  });
</script> 