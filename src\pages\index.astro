---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;

// Get portfolio projects for featured section
const portfolioProjects = await getCollection('portfolio');
const featuredProjects = portfolioProjects
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3); // Show latest 3 projects
---

<Layout title="Nob Hokleng | Software Developer & System Architect">
  <!-- Hero Section -->
  <section class="hero min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden" role="banner">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-orange-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>
    
    <div class="container mx-auto px-6 max-w-7xl relative z-10">
      <div class="grid lg:grid-cols-2 gap-16 items-center">
        <!-- Content Side -->
        <div class="space-y-8">
          <div class="space-y-6">
            <div class="inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-blue-200/50 shadow-lg">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
              <span class="text-sm font-medium text-slate-700">Available for new opportunities</span>
            </div>
            
            <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold font-heading leading-tight">
              <span class="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent" set:html={hero.headline}></span>
            </h1>
            
            <h2 class="text-xl sm:text-2xl lg:text-3xl text-blue-600 font-semibold">
              {hero.subheadline}
            </h2>
            
            <p class="text-lg text-slate-600 leading-relaxed max-w-2xl">
              {hero.description}
            </p>
          </div>
          
          <!-- Tech Stack Pills -->
          <div class="flex flex-wrap gap-3">
            {hero.highlights.map((highlight) => (
              <div class="flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-xl border border-slate-200/50 hover:bg-white/80 hover:scale-105 transition-all duration-300 shadow-sm hover:shadow-md">
                <span class="text-lg">{highlight.icon}</span>
                <span class="font-medium text-slate-700 text-sm">{highlight.label}</span>
              </div>
            ))}
          </div>
          
          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 pt-4">
            <a 
              href={hero.primaryCTA.url} 
              class="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
              aria-label="View my portfolio projects"
            >
              {hero.primaryCTA.text}
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a 
              href={hero.secondaryCTA.url} 
              class="inline-flex items-center justify-center px-8 py-4 bg-white/80 backdrop-blur-sm text-slate-700 rounded-2xl font-semibold border border-slate-200/50 hover:bg-white hover:scale-105 transition-all duration-300 shadow-sm hover:shadow-md"
              aria-label="Get in touch with me"
            >
              {hero.secondaryCTA.text}
            </a>
          </div>
        </div>
        
        <!-- Visual Side -->
        <div class="relative">
          <div class="relative mx-auto w-80 h-80 lg:w-96 lg:h-96">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-2xl opacity-20 animate-pulse"></div>
            <div class="absolute inset-4 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full blur-xl opacity-30 animate-pulse delay-500"></div>
            
            <!-- Main image -->
            <div class="relative z-10 w-full h-full">
              <img
                src="/headshot.jpg"
                alt="Nob Hokleng - Software Developer & System Architect"
                width="400"
                height="400"
                class="w-full h-full object-cover rounded-3xl shadow-2xl border-4 border-white/50 hover:scale-105 transition-transform duration-500"
                loading="eager"
              />
              
              <!-- Floating elements -->
              <div class="absolute -top-4 -right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg flex items-center justify-center animate-bounce">
                <span class="text-2xl">💻</span>
              </div>
              <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg flex items-center justify-center animate-bounce delay-300">
                <span class="text-2xl">⚡</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about py-32 bg-gradient-to-br from-white to-slate-50" role="main">
    <div class="container mx-auto px-6 max-w-7xl">
      <div class="text-center mb-20">
        <h2 class="text-4xl md:text-5xl font-bold font-heading mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
          About Me
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto rounded-full"></div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start">
        <div class="space-y-8">
          <div class="prose prose-lg">
            <p class="text-xl font-medium text-slate-700 leading-relaxed mb-8">
              {about.openingLine}
            </p>
            {about.mainContent.map((paragraph) => (
              <p class="text-slate-600 leading-relaxed mb-6">
                {paragraph}
              </p>
            ))}
          </div>
          
          <div class="experience-highlights">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 font-heading">Experience Highlights</h3>
            <div class="space-y-4">
              {about.experienceHighlights.map((highlight) => (
                <div class="flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mt-0.5">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <span class="text-slate-700 font-medium">{highlight}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div class="skills-section">
          <div class="bg-white/80 backdrop-blur-sm p-8 rounded-3xl shadow-xl border border-slate-200/50">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 text-center font-heading">Technical Skills</h3>
            <div class="grid grid-cols-1 gap-6">
              {about.skills.map((skillCategory) => (
                <div class="skill-category p-6 bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl border border-slate-200/30 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <h4 class="text-lg font-semibold text-blue-700 mb-4 font-heading flex items-center gap-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    {skillCategory.category}
                  </h4>
                  <div class="flex flex-wrap gap-2">
                    {skillCategory.items.map((skill) => (
                      <span class="px-3 py-1 bg-white/70 text-slate-700 text-sm font-medium rounded-lg border border-slate-200/50 hover:bg-white hover:scale-105 transition-all duration-200">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="portfolio py-32 bg-gradient-to-br from-slate-100 to-blue-50 relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Background decoration -->
    <div class="absolute inset-0">
      <div class="absolute top-1/3 left-1/3 w-64 h-64 bg-gradient-to-r from-blue-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-orange-400/5 to-pink-400/5 rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-6 max-w-7xl relative z-10">
      <div class="text-center mb-20">
        <h2 id="portfolio-title" class="text-4xl md:text-5xl font-bold font-heading mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
          Featured Projects
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto rounded-full mb-6"></div>
        <p class="text-lg text-slate-600 max-w-2xl mx-auto">
          A showcase of my recent work in software development and system architecture
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        {featuredProjects.map((project, index) => (
          <article class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg border border-slate-200/50 overflow-hidden hover:shadow-2xl hover:scale-105 transition-all duration-500 hover:bg-white" role="listitem">
            <div class="p-8 h-full flex flex-col">
              <!-- Project number badge -->
              <div class="flex items-center justify-between mb-6">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                  {(index + 1).toString().padStart(2, '0')}
                </div>
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </div>
              </div>
              
              <div class="flex-grow">
                <h3 class="text-xl font-bold text-slate-800 mb-4 font-heading group-hover:text-blue-700 transition-colors duration-300">
                  {project.data.title}
                </h3>
                <p class="text-slate-600 leading-relaxed mb-6 line-clamp-3">
                  {project.data.problem}
                </p>
              </div>
              
              <!-- Tags -->
              <div class="flex flex-wrap gap-2 mt-auto">
                {project.data.technologies.slice(0, 3).map(tag => (
                  <span class="px-3 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 text-xs font-medium rounded-full border border-blue-200/50 hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 transition-all duration-200">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </article>
        ))}
      </div>
      
      <div class="text-center mt-16">
        <a href="/portfolio" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300">
          View All Projects
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</Layout> 