# NobiSite - Professional Portfolio Website

> 🚀 **Modern Portfolio Website** - Built with performance and professional standards in mind

![NobiSite Screenshot](./docs/images/screenshot.png)

## About

A modern, high-performance portfolio website template designed for software engineers and developers. This project demonstrates professional development practices, modern technology choices, and comprehensive project planning methodologies.

**Use Case:** Professional portfolio for software engineers, developers, and tech professionals seeking to showcase their work and attract opportunities.

## Project Status

**Current Phase:** In Development 🚀
**Development Status:** Actively being built with Astro.js and Tailwind CSS.

### Implementation Phases
- [x] **Planning & Documentation**
- [x] **Technology Stack Selection**
- [ ] **Core Feature Development**
- [ ] **Performance & SEO Optimization**
- [ ] **Testing & Deployment**

### Quality Targets
- **Performance:** 95+ Lighthouse score, <1.2s load time
- **Accessibility:** WCAG 2.1 AA compliance
- **SEO:** Comprehensive optimization with structured data
- **Code Quality:** TypeScript, modern development practices

## Features & Capabilities

### Core Features
- **Professional Homepage** - Hero section with value proposition and call-to-action
- **Portfolio Showcase** - Detailed project case studies with GitHub integration
- **About Section** - Professional journey, technical expertise, and leadership experience
- **Interactive Resume** - Downloadable PDF with skills matrix and achievements
- **Contact Integration** - Professional contact form with social media links
- **Resources & RSS Feed** - Curated bookmarks and technical content subscription

### Technical Features
- **Performance Optimized** - 95+ Lighthouse scores, <1.2s load time
- **SEO Excellence** - Automatic sitemap, structured data, meta tag management
- **Accessibility Compliant** - WCAG 2.1 AA standards with keyboard navigation
- **Mobile-First Design** - Responsive across all devices and screen sizes
- **Interactive UI Components** - Smooth page transitions, responsive navigation, and a filterable portfolio/resource section.
- **Modern Development** - TypeScript, component architecture, automated deployment

### Development Showcase
- **Modern Tech Stack** - Demonstrates current industry best practices
- **Performance Focus** - Optimized for speed and user experience
- **Professional Standards** - Clean code, documentation, testing
- **Responsive Design** - Works seamlessly across all devices

## Technology Stack

### Selected Modern Stack
- **Frontend Framework:** Astro.js with TypeScript
- **CSS Framework:** Tailwind CSS with custom design system
- **Hosting Platform:** Vercel (Primary) + Netlify (Backup)
- **Content Management:** MDX with Frontmatter
- **Build Tools:** Vite (integrated with Astro)
- **Analytics:** Vercel Analytics + Google Analytics 4
- **Package Manager:** pnpm for faster dependency management

### Technology Selection Rationale

| Aspect | Astro.js Choice | Benefits |
|--------|----------------|----------|
| **Performance** | Zero-JS by default | 95+ Lighthouse scores, <1.2s load time |
| **SEO** | Built-in SSG | Automatic sitemap, meta tags, structured data |
| **Developer Experience** | Modern tooling | Hot reload, TypeScript, component architecture |
| **Maintainability** | Component-based | Reusable components, utility-first CSS |
| **Learning Curve** | Minimal | Familiar HTML/CSS/JS syntax |
| **Portfolio Fit** | Perfect match | Designed for content-focused sites |

## Getting Started

### Prerequisites
- Node.js (v18 or higher recommended)
- pnpm

### Quick Start
```bash
# 1. Clone the repository
git clone https://github.com/Nobhokleng/nobi-site.git
cd nobi-site

# 2. Install dependencies
pnpm install

# 3. Run the development server
pnpm dev

# 4. Open your browser to http://localhost:4321
```

### Production Deployment
- **Recommended:** Vercel with automatic GitHub integration.
- **Features:** Custom domain, automatic HTTPS, performance monitoring, and preview deployments.

## Documentation

### 📚 Comprehensive Project Documentation
All project planning, technical specifications, and implementation guides are available in the [`docs/`](./docs/) folder:

- **[Project Plan](./docs/project_plan.md)** - Timeline, phases, and implementation strategy
- **[Technology Selection](./docs/technology_selection.md)** - Stack decisions and rationale
- **[Technical Specifications](./docs/technical_specifications.md)** - Requirements and quality standards
- **[Design Guidelines](./docs/design_guidelines.md)** - Visual design and Tailwind configuration
- **[Content Plan](./docs/content_plan.md)** - Content strategy and templates

## Contributing

This project serves as a portfolio template and demonstration of modern web development practices. Feel free to use it as inspiration for your own portfolio projects.

## License

This project is open source and available under the [MIT License](LICENSE).

---

**Note:** This project demonstrates professional development practices including comprehensive documentation, modern technology choices, and quality-focused implementation.

## Contact

Feel free to reach out if you have any questions or suggestions.

---

**Note:** This README will be updated regularly as the project progresses. Check back for updates on development progress and final features.
