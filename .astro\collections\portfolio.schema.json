{"$ref": "#/definitions/portfolio", "definitions": {"portfolio": {"type": "object", "properties": {"title": {"type": "string"}, "publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "problem": {"type": "string"}, "solution": {"type": "string"}, "technologies": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string"}, "results": {"type": "string"}, "repoUrl": {"type": "string", "format": "uri"}, "liveUrl": {"type": "string", "format": "uri"}, "heroImage": {"type": "string"}, "$schema": {"type": "string"}}, "required": ["title", "publishDate", "problem", "solution", "technologies", "role", "results", "heroImage"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}