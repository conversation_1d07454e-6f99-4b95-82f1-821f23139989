---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
  index?: number;
}

const { title, description, tags, slug, image, index = 0 } = Astro.props;
---

<article class="group relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg border border-slate-200/50 overflow-hidden hover:shadow-2xl hover:scale-105 transition-all duration-500 hover:bg-white min-h-96 flex flex-col" role="listitem">
  <!-- Gradient overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

  <!-- Project number badge -->
  <div class="absolute top-6 left-6 z-10">
    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
      {(index + 1).toString().padStart(2, '0')}
    </div>
  </div>

  <!-- Action indicator -->
  <div class="absolute top-6 right-6 z-10 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
    <div class="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
      <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 p-8 flex flex-col flex-grow">
    <!-- Header spacing for badges -->
    <div class="h-6 mb-4"></div>

    <div class="flex-grow">
      <h3 class="text-2xl font-bold text-slate-800 mb-4 font-heading group-hover:text-blue-700 transition-colors duration-300">
        {title}
      </h3>
      <p class="text-slate-600 leading-relaxed mb-6 line-clamp-3">
        {description}
      </p>
    </div>

    <!-- Enhanced tags -->
    <div class="flex flex-wrap gap-2 mt-auto">
      {tags.slice(0, 3).map((tag, tagIndex) => (
        <span
          class="px-4 py-2 bg-gradient-to-r from-slate-100 to-blue-50 text-slate-700 text-sm font-semibold rounded-xl border border-slate-200/50 hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 transition-all duration-300 hover:scale-105 hover:shadow-md"
          style={`animation-delay: ${tagIndex * 100}ms`}
        >
          {tag}
        </span>
      ))}
      {tags.length > 3 && (
        <span class="px-4 py-2 bg-gradient-to-r from-slate-200 to-slate-100 text-slate-600 text-sm font-semibold rounded-xl border border-slate-200/50">
          +{tags.length - 3}
        </span>
      )}
    </div>

    <!-- Hover effect line -->
    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
  </div>
</article>