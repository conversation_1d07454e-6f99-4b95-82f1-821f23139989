---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
}

const { title, description, tags, slug } = Astro.props;
---

<article class="portfolio-item bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 relative border-t-4 border-gradient-to-r from-primary to-accent min-h-80 flex flex-col" role="listitem">
  <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-accent"></div>
  <div class="portfolio-content p-8 text-center flex flex-col flex-grow justify-between">
    <div>
      <h3 class="text-xl font-semibold text-secondary mb-4 font-heading">{title}</h3>
      <p class="text-gray-600 leading-relaxed text-sm mb-5 flex-grow">{description}</p>
    </div>
    <div class="project-tags flex flex-wrap gap-2 justify-center mt-auto">
      {tags.map(tag => (
        <span class="tag px-3 py-1 bg-gradient-to-br from-primary/10 to-secondary/10 text-primary text-xs font-medium rounded-full border border-primary/20 hover:bg-gradient-to-br hover:from-primary/15 hover:to-secondary/15 transition-all duration-300 hover:-translate-y-0.5">
          {tag}
        </span>
      ))}
    </div>
  </div>
</article> 