---
interface Props {
  variant?: 'default' | 'compact' | 'contact';
  className?: string;
}

const { variant = 'default', className = '' } = Astro.props;

const socialLinks = [
  {
    name: 'LinkedIn',
    url: 'https://linkedin.com/in/nobhokleng',
    icon: 'fab fa-linkedin',
    color: 'from-blue-600 to-blue-700',
    description: 'Professional network'
  },
  {
    name: 'GitHub',
    url: 'https://github.com/Nobhokleng',
    icon: 'fab fa-github',
    color: 'from-gray-800 to-black',
    description: 'Code repositories'
  },
  {
    name: 'Email',
    url: 'mailto:<EMAIL>',
    icon: 'fas fa-envelope',
    color: 'from-primary to-secondary',
    description: '<EMAIL>'
  }
];
---

<div class={`social-links ${className}`}>
  {variant === 'compact' && (
    <div class="flex gap-4">
      {socialLinks.map(link => (
        <a 
          href={link.url} 
          target={link.name !== 'Email' ? '_blank' : undefined}
          rel={link.name !== 'Email' ? 'noopener noreferrer' : undefined}
          class="social-icon w-12 h-12 rounded-full bg-gradient-to-r text-white flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 hover:scale-110"
          class:list={[`bg-gradient-to-r ${link.color}`]}
          aria-label={`${link.name} - ${link.description}`}
        >
          <i class={`${link.icon} text-lg`} aria-hidden="true"></i>
        </a>
      ))}
    </div>
  )}

  {variant === 'contact' && (
    <div class="space-y-4">
      {socialLinks.map(link => (
        <a 
          href={link.url} 
          target={link.name !== 'Email' ? '_blank' : undefined}
          rel={link.name !== 'Email' ? 'noopener noreferrer' : undefined}
          class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
          class:list={[`bg-gradient-to-r ${link.color}`]}
          aria-label={`${link.name} - ${link.description}`}
        >
          <i class={`${link.icon} text-lg group-hover:scale-110 transition-transform`} aria-hidden="true"></i>
          <div class="flex flex-col">
            <span class="font-semibold">{link.name}</span>
            <span class="text-xs opacity-90">{link.description}</span>
          </div>
        </a>
      ))}
    </div>
  )}

  {variant === 'default' && (
    <div class="flex gap-6">
      {socialLinks.map(link => (
        <a 
          href={link.url} 
          target={link.name !== 'Email' ? '_blank' : undefined}
          rel={link.name !== 'Email' ? 'noopener noreferrer' : undefined}
          class="social-link group flex items-center gap-3 px-6 py-3 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100"
          aria-label={`${link.name} - ${link.description}`}
        >
          <div class="w-10 h-10 rounded-full bg-gradient-to-r flex items-center justify-center text-white group-hover:scale-110 transition-transform"
               class:list={[`bg-gradient-to-r ${link.color}`]}>
            <i class={`${link.icon} text-sm`} aria-hidden="true"></i>
          </div>
          <div class="flex flex-col">
            <span class="font-semibold text-secondary">{link.name}</span>
            <span class="text-xs text-gray-500">{link.description}</span>
          </div>
        </a>
      ))}
    </div>
  )}
</div>

<style>
  .social-links a:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
  
  .social-links a:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
</style>