---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import <PERSON>sonLd from '../components/JsonLd.astro';
import DarkModeToggle from '../components/DarkModeToggle.astro';
import '../styles/global.css';

interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  ogType?: string;
  keywords?: string;
}

const {
  title = 'Nob Hokleng | Software Developer & System Architect',
  description = 'Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.',
  ogImage = 'https://nobhokleng.dev/headshot.jpg',
  ogType = 'website',
  keywords = 'software developer, backend developer, system architecture, scalable systems, web development, programming'
} = Astro.props;

// Get the current URL for canonical and Open Graph
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title}>
    <meta name="description" content={description}>
    <meta name="keywords" content={keywords}>
    <meta name="author" content="Nob Hokleng">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={ogType}>
    <meta property="og:url" content={canonicalURL}>
    <meta property="og:title" content={title}>
    <meta property="og:description" content={description}>
    <meta property="og:image" content={ogImage}>
    <meta property="og:site_name" content="Nob Hokleng">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content={canonicalURL}>
    <meta property="twitter:title" content={title}>
    <meta property="twitter:description" content={description}>
    <meta property="twitter:image" content={ogImage}>

    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL}>

    <!-- RSS Feed -->
    <link rel="alternate" type="application/rss+xml" title="Nob Hokleng | Developer Resources" href="/rss.xml">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦕</text></svg>">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://github.com">
    <link rel="dns-prefetch" href="https://linkedin.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">

    <!-- Stylesheets with optimized loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer"></noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family:Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet"></noscript>

    <!-- Structured Data -->
    <JsonLd type="person" />
  </head>
  <body class="min-h-screen flex flex-col">
    <!-- Skip to main content link for screen readers -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-accent">
      Skip to main content
    </a>
    <Header />
    <main id="main-content" class="flex-grow">
      <slot />
    </main>
    <Footer />
  </body>
</html> 