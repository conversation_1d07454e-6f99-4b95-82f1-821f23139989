---
// Dark Mode Toggle Component
---

<button
  id="dark-mode-toggle"
  class="fixed bottom-6 right-6 z-50 w-14 h-14 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl shadow-lg hover:shadow-xl border border-slate-200/50 dark:border-slate-700/50 transition-all duration-300 hover:scale-105 group"
  aria-label="Toggle dark mode"
  title="Toggle dark mode"
>
  <!-- Sun icon (visible in dark mode) -->
  <svg
    id="sun-icon"
    class="w-6 h-6 text-yellow-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300 opacity-0 scale-0 dark:opacity-100 dark:scale-100"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
    ></path>
  </svg>

  <!-- Moon icon (visible in light mode) -->
  <svg
    id="moon-icon"
    class="w-6 h-6 text-slate-700 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300 opacity-100 scale-100 dark:opacity-0 dark:scale-0"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
    ></path>
  </svg>

  <!-- Ripple effect -->
  <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
</button>

<script>
  // Dark mode functionality
  function initDarkMode() {
    const toggle = document.getElementById('dark-mode-toggle');
    const html = document.documentElement;
    
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Set initial theme
    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }
    
    // Toggle function
    function toggleDarkMode() {
      const isDark = html.classList.contains('dark');
      
      if (isDark) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      }
      
      // Add a subtle animation to the toggle button
      if (toggle) {
        toggle.style.transform = 'scale(0.95)';
        setTimeout(() => {
          toggle.style.transform = 'scale(1)';
        }, 150);
      }
    }
    
    // Event listener
    if (toggle) {
      toggle.addEventListener('click', toggleDarkMode);
    }
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        if (e.matches) {
          html.classList.add('dark');
        } else {
          html.classList.remove('dark');
        }
      }
    });
  }
  
  // Initialize when DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initDarkMode);
  } else {
    initDarkMode();
  }
</script>

<style>
  #dark-mode-toggle {
    transition: transform 0.15s ease;
  }
  
  #dark-mode-toggle:active {
    transform: scale(0.95);
  }
</style>
