@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles for layout structure */
body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2b2d42;
  line-height: 1.6;
  background-color: #ffffff;
}

main {
  flex-grow: 1;
}

/* Custom scrolling for smooth navigation */
html {
  scroll-behavior: smooth;
}

/* Animation keyframes */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(255, 158, 0, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 