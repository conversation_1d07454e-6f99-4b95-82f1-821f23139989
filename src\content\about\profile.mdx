---
title: "About Nob Hokleng"
updatedDate: 2024-01-01
sections:
  - heading: "Professional Journey"
    content: "With a Bachelor's degree in Computer Science and 5 years of hands-on experience in software development, my journey has been driven by a passion for building robust, scalable systems. I've evolved from writing my first Java applications to architecting comprehensive DevOps pipelines and developing complex microservices that power modern applications.\n\nMy expertise spans both DevOps and Software Development, allowing me to bridge the gap between development and operations. From implementing CI/CD pipelines to developing payment gateway integrations, each project has strengthened my commitment to delivering reliable, efficient solutions."
  - heading: "Philosophy & Approach"
    content: "Over the years, I've evolved from focusing purely on code to understanding the broader picture – how technology serves business goals, how teams collaborate effectively, and how great architecture enables innovation."
    subsections:
      - subheading: "Core Values"
        items:
          - "Continuous learning and growth"
          - "Open communication and feedback"
          - "Quality over speed (but efficiency matters)"
          - "Building inclusive and supportive teams"
  - heading: "DevOps Expertise"
    content: "My DevOps expertise encompasses the entire software development lifecycle, from source code management to production monitoring and maintenance."
    subsections:
      - subheading: "Source Code & Repository Management"
        items:
          - "Proficient in Gitea and GitHub for robust version control"
          - "Experienced with Nexus for efficient artifact and dependency management"
      - subheading: "Project Management & Development Tools"
        items:
          - "Skilled in Jira, Trello, ClickUp, and Redmine for streamlined project organization"
          - "Proficient in IntelliJ, Visual Studio Code, and GitKraken"
      - subheading: "Testing & Quality Assurance"
        items:
          - "Experienced in Postman, Swagger API, and JProfile"
          - "Proficient in penetration and stress testing methodologies"
      - subheading: "Monitoring & Server Management"
        items:
          - "Adept at monitoring server and service performance with Monit"
          - "Experience in configuring, deploying, and managing servers (Apache, Nginx)"
          - "Proficient with cloud-based services (AWS, Digital Ocean)"
          - "Skilled in maintaining audit logs for compliance and analysis"
  - heading: "Software Development Expertise"
    content: "My software development expertise is built on a strong foundation in Java and Groovy, with deep knowledge of modern frameworks and architectural patterns that enable the creation of scalable, maintainable applications."
    subsections:
      - subheading: "Programming Languages"
        items:
          - "Proficient in Java and Groovy with deep knowledge of core concepts"
          - "Strong understanding of object-oriented programming principles"
      - subheading: "Frameworks & Architecture"
        items:
          - "Spring Boot for enterprise application development"
          - "Grails for rapid web application development"
          - "Micronaut Framework for microservices architecture"
      - subheading: "API Development & Integration"
        items:
          - "Proficiency in designing and developing various APIs"
          - "Payment gateway integration experience (ABA, Wing, Cellcard)"
          - "Secure communication with external systems"
      - subheading: "Database & Data Management"
        items:
          - "Relational databases: MySQL and MariaDB"
          - "NoSQL databases: MongoDB and Cassandra"
          - "Message Brokers: RabbitMQ and Kafka"
          - "Data storage: Minio, AWS S3, and DigitalOcean Spaces"
---

## My Development Philosophy

Building software isn't just about writing code—it's about creating reliable, scalable solutions that solve real-world problems. With my comprehensive background in both DevOps and Software Development, I bring a holistic approach to every project.

## Technical Expertise in Action

My experience spans the entire software development lifecycle, from initial architecture design to production deployment and monitoring. I've worked extensively with:

### Enterprise Applications
- **Java & Groovy Development**: Building robust backend systems using Spring Boot, Grails, and Micronaut
- **Payment Gateway Integration**: Implementing secure payment solutions with ABA, Wing, and Cellcard
- **Database Architecture**: Designing efficient data models across MySQL, MariaDB, MongoDB, and Cassandra

### DevOps & Infrastructure
- **CI/CD Pipelines**: Streamlining development workflows with automated testing and deployment
- **Cloud Infrastructure**: Managing scalable deployments on AWS and Digital Ocean
- **Monitoring & Maintenance**: Ensuring system reliability with comprehensive monitoring solutions

### Data & Messaging
- **Message Brokers**: Implementing asynchronous communication with RabbitMQ and Kafka
- **Data Storage**: Managing large-scale data with S3-compatible solutions (Minio, AWS S3, DigitalOcean Spaces)

## Continuous Learning

Technology evolves rapidly, and I'm committed to staying current with industry trends and best practices. My 5 years of experience have taught me that the best solutions come from understanding both the technical requirements and the business context.