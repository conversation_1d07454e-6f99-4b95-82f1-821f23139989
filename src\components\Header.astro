<header role="banner" class="fixed w-full top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-lg shadow-black/5 transition-all duration-300">
  <!-- Glassmorphism background overlay -->
  <div class="absolute inset-0 bg-gradient-to-r from-white/90 via-white/80 to-white/90 backdrop-blur-xl"></div>
  <div class="container mx-auto px-6 py-5 flex justify-between items-center max-w-6xl relative z-10">
    <a href="/" class="logo text-3xl font-bold text-primary font-heading relative group flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105" aria-label="Nob Hokleng - Home">
      <span class="text-white font-extrabold">NH</span>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-700 to-indigo-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </a>

    <!-- Enhanced Desktop Navigation -->
    <nav role="navigation" aria-label="Main navigation" class="hidden md:block">
      <ul class="flex items-center space-x-2 bg-white/60 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/30 shadow-lg">
        <li><a href="/#about" class="nav-link text-slate-700 font-semibold transition-all duration-300 hover:text-blue-600 relative px-4 py-2 rounded-xl hover:bg-white/80" aria-label="About me section">About</a></li>
        <li><a href="/#portfolio" class="nav-link text-slate-700 font-semibold transition-all duration-300 hover:text-blue-600 relative px-4 py-2 rounded-xl hover:bg-white/80" aria-label="Portfolio projects section">Portfolio</a></li>
        <li><a href="/resume" class="nav-link text-slate-700 font-semibold transition-all duration-300 hover:text-blue-600 relative px-4 py-2 rounded-xl hover:bg-white/80" aria-label="Resume and experience section">Resume</a></li>
        <li><a href="/resources" class="nav-link text-slate-700 font-semibold transition-all duration-300 hover:text-blue-600 relative px-4 py-2 rounded-xl hover:bg-white/80" aria-label="Resources and RSS feed section">Resources</a></li>
        <li><a href="/contact" class="nav-link text-white font-semibold transition-all duration-300 relative px-4 py-2 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl hover:scale-105" aria-label="Contact information section">Contact</a></li>
      </ul>
    </nav>

    <!-- Enhanced Mobile Menu Button -->
    <button
      id="mobile-menu-button"
      class="md:hidden flex flex-col justify-center items-center w-12 h-12 bg-white/80 backdrop-blur-md rounded-2xl border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500"
      aria-label="Toggle mobile navigation menu"
      aria-expanded="false"
      aria-controls="mobile-menu"
    >
      <span class="w-6 h-0.5 bg-slate-700 transition-all duration-300"></span>
      <span class="w-6 h-0.5 bg-slate-700 transition-all duration-300 my-1"></span>
      <span class="w-6 h-0.5 bg-slate-700 transition-all duration-300"></span>
    </button>
  </div>

  <!-- Enhanced Mobile Navigation Menu -->
  <nav
    id="mobile-menu"
    class="md:hidden bg-white/95 backdrop-blur-xl border-t border-white/20 shadow-xl hidden transform transition-all duration-300"
    role="navigation"
    aria-label="Mobile navigation"
  >
    <div class="absolute inset-0 bg-gradient-to-b from-white/90 to-white/95 backdrop-blur-xl"></div>
    <ul class="py-6 space-y-1 relative z-10">
      <li><a href="/#about" class="block mx-4 px-6 py-4 text-slate-700 font-semibold hover:bg-white/80 hover:text-blue-600 transition-all duration-300 rounded-2xl hover:scale-105 hover:shadow-lg" aria-label="About me section">About</a></li>
      <li><a href="/#portfolio" class="block mx-4 px-6 py-4 text-slate-700 font-semibold hover:bg-white/80 hover:text-blue-600 transition-all duration-300 rounded-2xl hover:scale-105 hover:shadow-lg" aria-label="Portfolio projects section">Portfolio</a></li>
      <li><a href="/resume" class="block mx-4 px-6 py-4 text-slate-700 font-semibold hover:bg-white/80 hover:text-blue-600 transition-all duration-300 rounded-2xl hover:scale-105 hover:shadow-lg" aria-label="Resume and experience section">Resume</a></li>
      <li><a href="/resources" class="block mx-4 px-6 py-4 text-slate-700 font-semibold hover:bg-white/80 hover:text-blue-600 transition-all duration-300 rounded-2xl hover:scale-105 hover:shadow-lg" aria-label="Resources and RSS feed section">Resources</a></li>
      <li><a href="/contact" class="block mx-4 px-6 py-4 text-white font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 rounded-2xl hover:scale-105 shadow-lg hover:shadow-xl" aria-label="Contact information section">Contact</a></li>
    </ul>
  </nav>

  <!-- Scroll Progress Indicator -->
  <div id="scroll-progress" class="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 transition-all duration-300 ease-out" style="width: 0%"></div>
</header>

<style>
.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3a86ff, #ff9e00);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Mobile menu button animation */
#mobile-menu-button.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

#mobile-menu-button.active span:nth-child(2) {
  opacity: 0;
}

#mobile-menu-button.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}
</style>

<script>
  // Mobile menu functionality
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', function() {
        const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';

        // Toggle menu visibility
        mobileMenu.classList.toggle('hidden');

        // Update ARIA attributes
        mobileMenuButton.setAttribute('aria-expanded', String(!isExpanded));

        // Toggle button animation
        mobileMenuButton.classList.toggle('active');
      });

      // Close menu when clicking on a link
      const mobileLinks = mobileMenu.querySelectorAll('a');
      mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
        });
      });

      // Close menu when clicking outside
      document.addEventListener('click', function(event) {
        const target = event.target as Node;
        if (!mobileMenuButton.contains(target) && !mobileMenu.contains(target)) {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
        }
      });

      // Handle escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
          mobileMenuButton.focus();
        }
      });
    }

    // Scroll progress indicator
    const scrollProgress = document.getElementById('scroll-progress');
    if (scrollProgress) {
      function updateScrollProgress() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollPercentage = (scrollTop / scrollHeight) * 100;
        scrollProgress.style.width = `${Math.min(scrollPercentage, 100)}%`;
      }

      window.addEventListener('scroll', updateScrollProgress);
      updateScrollProgress(); // Initial call
    }

    // Header background opacity on scroll
    const header = document.querySelector('header');
    if (header) {
      function updateHeaderOpacity() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const opacity = Math.min(scrollTop / 100, 1);
        header.style.backgroundColor = `rgba(255, 255, 255, ${0.8 + opacity * 0.15})`;
      }

      window.addEventListener('scroll', updateHeaderOpacity);
      updateHeaderOpacity(); // Initial call
    }
  });
</script>