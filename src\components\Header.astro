<header role="banner" class="fixed w-full top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-50 shadow-sm">
  <div class="container mx-auto px-5 py-4 flex justify-between items-center max-w-6xl">
    <a href="/" class="logo text-2xl font-bold text-primary font-heading relative group" aria-label="Nob Hokleng - Home">
      NH
      <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-accent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
    </a>

    <!-- Desktop Navigation -->
    <nav role="navigation" aria-label="Main navigation" class="hidden md:block">
      <ul class="flex space-x-8">
        <li><a href="/#about" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="About me section">About</a></li>
        <li><a href="/#portfolio" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Portfolio projects section">Portfolio</a></li>
        <li><a href="/resume" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Resume and experience section">Resume</a></li>
        <li><a href="/resources" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Resources and RSS feed section">Resources</a></li>
        <li><a href="/contact" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Contact information section">Contact</a></li>
      </ul>
    </nav>

    <!-- Mobile Menu Button -->
    <button
      id="mobile-menu-button"
      class="md:hidden flex flex-col justify-center items-center w-8 h-8 space-y-1 focus:outline-none focus:ring-2 focus:ring-primary rounded"
      aria-label="Toggle mobile navigation menu"
      aria-expanded="false"
      aria-controls="mobile-menu"
    >
      <span class="w-6 h-0.5 bg-text transition-all duration-300"></span>
      <span class="w-6 h-0.5 bg-text transition-all duration-300"></span>
      <span class="w-6 h-0.5 bg-text transition-all duration-300"></span>
    </button>
  </div>

  <!-- Mobile Navigation Menu -->
  <nav
    id="mobile-menu"
    class="md:hidden bg-white border-t border-gray-100 shadow-lg hidden"
    role="navigation"
    aria-label="Mobile navigation"
  >
    <ul class="py-4 space-y-2">
      <li><a href="/#about" class="block px-5 py-3 text-text font-medium hover:bg-light hover:text-primary transition-colors" aria-label="About me section">About</a></li>
      <li><a href="/#portfolio" class="block px-5 py-3 text-text font-medium hover:bg-light hover:text-primary transition-colors" aria-label="Portfolio projects section">Portfolio</a></li>
      <li><a href="/resume" class="block px-5 py-3 text-text font-medium hover:bg-light hover:text-primary transition-colors" aria-label="Resume and experience section">Resume</a></li>
      <li><a href="/resources" class="block px-5 py-3 text-text font-medium hover:bg-light hover:text-primary transition-colors" aria-label="Resources and RSS feed section">Resources</a></li>
      <li><a href="/contact" class="block px-5 py-3 text-text font-medium hover:bg-light hover:text-primary transition-colors" aria-label="Contact information section">Contact</a></li>
    </ul>
  </nav>
</header>

<style>
.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3a86ff, #ff9e00);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Mobile menu button animation */
#mobile-menu-button.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

#mobile-menu-button.active span:nth-child(2) {
  opacity: 0;
}

#mobile-menu-button.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}
</style>

<script>
  // Mobile menu functionality
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', function() {
        const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';

        // Toggle menu visibility
        mobileMenu.classList.toggle('hidden');

        // Update ARIA attributes
        mobileMenuButton.setAttribute('aria-expanded', String(!isExpanded));

        // Toggle button animation
        mobileMenuButton.classList.toggle('active');
      });

      // Close menu when clicking on a link
      const mobileLinks = mobileMenu.querySelectorAll('a');
      mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
        });
      });

      // Close menu when clicking outside
      document.addEventListener('click', function(event) {
        const target = event.target as Node;
        if (!mobileMenuButton.contains(target) && !mobileMenu.contains(target)) {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
        }
      });

      // Handle escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
          mobileMenu.classList.add('hidden');
          mobileMenuButton.setAttribute('aria-expanded', 'false');
          mobileMenuButton.classList.remove('active');
          mobileMenuButton.focus();
        }
      });
    }
  });
</script>