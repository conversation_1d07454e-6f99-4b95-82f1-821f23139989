import rss from '@astrojs/rss';
import { getCollection } from 'astro:content';

export async function GET(context) {
  // Fetch all resources from the content collection
  const resources = await getCollection('resources');
  
  // Sort resources by title for consistent ordering
  const sortedResources = resources.sort((a, b) => a.data.title.localeCompare(b.data.title));

  return rss({
    // RSS feed metadata
    title: 'Nob Hokleng | Developer Resources',
    description: 'Curated collection of development resources, tools, and documentation for software engineers and system architects.',
    site: context.site,
    
    // Generate RSS items from resources
    items: sortedResources.map((resource) => ({
      title: resource.data.title,
      description: resource.data.description,
      link: resource.data.url, // External URL to the actual resource
      pubDate: new Date(), // Since resources don't have dates, use current date
      categories: [resource.data.category, ...resource.data.tags],
      
      // Custom fields for additional metadata
      customData: `
        <category>${resource.data.category}</category>
        <tags>${resource.data.tags.join(', ')}</tags>
        <source>${resource.data.url}</source>
      `,
    })),
    
    // RSS feed configuration
    customData: `
      <language>en-us</language>
      <managingEditor><EMAIL> (Nob Hokleng)</managingEditor>
      <webMaster><EMAIL> (Nob Hokleng)</webMaster>
      <category>Technology</category>
      <category>Software Development</category>
      <category>Programming</category>
    `,
  });
}
