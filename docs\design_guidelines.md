# Design Guidelines

## Brand Identity

### Personal Brand Statement
"Professional software developer with experience building scalable systems and working with technical teams"

### Voice & Tone
- Professional but approachable
- Technical but clear
- Confident but not arrogant
- Solution-oriented and practical

## Visual Design

### Color Palette (Tailwind Configuration)
- **Primary Color:** `#3a86ff` (blue-500 equivalent)
- **Secondary Color:** `#0a2463` (blue-900 equivalent)
- **Accent Color:** `#ff9e00` (orange-400 equivalent)
- **Neutral Colors:**
  - Background: `#ffffff` (white)
  - Light background: `#f8f9fa` (gray-50)
  - Border: `#e9ecef` (gray-200)
- **Text Colors:**
  - Primary text: `#2b2d42` (gray-800)
  - Light text: `#6c757d` (gray-500)

### Typography (Tailwind Configuration)
- **Headings:** Montserrat (`font-heading`) - Professional, modern sans-serif
- **Body Text:** Poppins (`font-sans`) - Readable, friendly sans-serif
- **Code Snippets:** Fira Code (`font-mono`) - Developer-focused monospace with ligatures
- **Font Sizes (Tailwind Classes):**
  - H1: `text-4xl md:text-5xl` (36px/48px)
  - H2: `text-2xl md:text-3xl` (24px/30px)
  - H3: `text-xl md:text-2xl` (20px/24px)
  - Body: `text-base md:text-lg` (16px/18px)
  - Small text: `text-sm` (14px)
- **Font Weights:**
  - Headings: `font-bold` (700) or `font-extrabold` (800)
  - Body: `font-normal` (400) or `font-medium` (500)
  - Emphasis: `font-semibold` (600)

### Layout Guidelines (Tailwind Implementation)
- **Design Philosophy:** Clean, minimal design with ample white space
- **Grid System:** CSS Grid and Flexbox via Tailwind utilities
- **Maximum Content Width:** `max-w-6xl` (1152px) with `container mx-auto`
- **Responsive Breakpoints (Tailwind):**
  - Mobile: Default (0px+) - `block`
  - Small: `sm:` (640px+) - `sm:flex`
  - Medium: `md:` (768px+) - `md:grid-cols-2`
  - Large: `lg:` (1024px+) - `lg:grid-cols-3`
  - Extra Large: `xl:` (1280px+) - `xl:max-w-7xl`
- **Spacing System:** Tailwind's 4px base unit (`space-y-4`, `p-6`, `m-8`)

### UI Elements (Tailwind Classes)
- **Buttons:**
  - Primary: `bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all`
  - Secondary: `border-2 border-primary text-primary hover:bg-primary hover:text-white px-6 py-3 rounded-lg font-semibold transition-all`
  - Text: `text-base` (16px), sentence case preferred
  - Interactive states: `hover:`, `focus:`, `active:` variants
  - Accessibility: `focus:ring-2 focus:ring-primary focus:ring-offset-2`

- **Cards/Containers:**
  - Base: `bg-white rounded-xl shadow-sm border border-gray-100`
  - Hover: `hover:shadow-md transition-shadow`
  - Padding: `p-6` (24px) or `p-8` (32px) for larger cards
  - Spacing: `space-y-4` for internal content

- **Navigation:**
  - Desktop: `hidden md:flex space-x-8`
  - Mobile: `md:hidden` with toggle button
  - Active state: `text-primary border-b-2 border-primary`
  - Hover: `hover:text-primary transition-colors`

### Imagery Guidelines
- Professional headshot with neutral background
- Project screenshots with consistent aspect ratios
- Simple icons for skills and technologies
- Minimal use of decorative elements

## Component Library

### Hero Section
- Full-width background (color or subtle pattern)
- Centered or left-aligned content
- Clear headline and subheading
- Optional background image or gradient

### Resources Section
- Card-based or list layout
- Category filtering options
- Thumbnail previews for videos
- Brief description for each resource
- Clear external links with proper styling

### About Section
- Professional photo
- Two-column layout on desktop
- Timeline or cards for experience

### Portfolio Projects
- Consistent card layout
- Featured image/screenshot
- Brief description
- Technologies used as tags
- Call-to-action link

### Skills Display
- Grouped by category
- Visual skill level indicators
- Clean, scannable layout

### Contact Form
- Minimal required fields
- Clear labels
- Visible submit button
- Success/error states

## Accessibility Considerations
- Minimum contrast ratio: 4.5:1
- Focus states for all interactive elements
- Text alternatives for all images
- Semantic HTML structure

### Responsive Design Principles
- **Mobile-First Approach:** Design for mobile first, then enhance for larger screens
- **Breakpoint Usage:** Use Tailwind's responsive prefixes consistently
- **Content Prioritization:** Determine content hierarchy for each breakpoint
- **Touch Targets:** Ensure interactive elements are at least 44×44px on mobile
- **Testing:** Test on actual devices, not just browser resizing
