{"$ref": "#/definitions/resources", "definitions": {"resources": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "description": {"type": "string"}, "category": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "$schema": {"type": "string"}}, "required": ["title", "url", "description", "category", "tags"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}