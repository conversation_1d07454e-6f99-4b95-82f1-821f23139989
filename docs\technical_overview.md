# Portfolio Website Technical Overview

## Technology Stack & Rationale

### Frontend Framework: Astro.js (v4.0+)
- **What**: Modern static site generator with "islands architecture"
- **Why**: Zero JavaScript by default for optimal performance (95+ Lighthouse score)
- **Key Features**: Component islands, file-based routing, multi-framework support
- **Benefits**: <1.2s First Contentful Paint, minimal JS footprint, SEO-friendly

### Styling: Tailwind CSS
- **What**: Utility-first CSS framework with custom design system
- **Why**: Rapid development, consistent design, minimal CSS output
- **Theme**: Custom colors (primary: #3a86ff, accent: #ff9e00)
- **Typography**: <PERSON>serrat (headings), <PERSON><PERSON>s (body), Fira Code (code)

### Type Safety: TypeScript
- **What**: Static type checking for JavaScript
- **Why**: Catches errors during development, improves code quality
- **Configuration**: Strict mode enabled, path aliases (@/* → src/*)

### Content Management: MDX + Content Collections
- **What**: Markdown with component support + type-safe content
- **Why**: Git-based content management without external CMS dependencies
- **Schema**: Zod validation for portfolio items, resources, and pages

## Project Architecture

```
src/
├── components/         # Reusable Astro components
│   ├── Header.astro    # Navigation component
│   ├── Footer.astro    # Footer component
│   └── ProjectCard.astro # Portfolio item display
├── content/            # MDX content with schemas
│   ├── config.ts       # Zod validation schemas
│   ├── portfolio/      # Project case studies
│   ├── resources/      # Resource links
│   └── about/          # About page content
├── layouts/            # Page templates
│   └── Layout.astro    # Main layout with SEO
├── pages/              # File-based routing
│   ├── index.astro     # Homepage
│   ├── about.astro     # About page
│   └── portfolio/      # Portfolio section
└── styles/             # Global CSS + Tailwind
```

## Performance & Quality Standards

### Performance Targets
- First Contentful Paint: < 1.2s
- Time to Interactive: < 2.5s
- Lighthouse Performance Score: > 95
- Cumulative Layout Shift: < 0.1
- Bundle Size: < 100KB (excluding images)

### SEO & Accessibility
- Semantic HTML with proper heading hierarchy
- Meta tags, Open Graph, and structured data
- WCAG 2.1 AA compliance for accessibility

## Development & Deployment

### Commands
```bash
npm run dev      # Start dev server with hot reload
npm run build    # Production build
npm run preview  # Preview built site locally
npm run check    # TypeScript validation
```

### Deployment
- **Platform**: Vercel (primary) or Netlify (backup)
- **Strategy**: Automatic deployments from GitHub
- **Optimization**: Edge network, asset optimization, caching
- **Environment Variables**: Configured via Vercel dashboard or .env files

## Browser Support
- Modern browsers (last 2 versions of Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari 14+, Android Chrome 90+)

## Why This Approach
This architecture provides an optimal balance of performance, developer experience, and maintainability for a professional portfolio website. The static-first approach ensures fast loading times and excellent SEO, while the component-based structure allows for easy updates and extensions.

Perfect for portfolios because:
- Lightning fast - Static sites load instantly
- SEO optimized - Great for discoverability
- Low maintenance - No servers or databases to manage
- Cost effective - Static hosting is often free
- Developer friendly - Modern tooling and type safety
- Future-proof - Easy to add dynamic features later
